using Microsoft.Extensions.Logging;
using HappyWechat.Domain.Entities;
using HappyWechat.Application.DTOs.Wx;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.DTOs.SystemConfig;
using HappyWechat.Application.Constants;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.MessageQueue.Simplified;
using HappyWechat.Infrastructure.Services;
using HappyWechat.Infrastructure.MediaProcessing;
using HappyWechat.Infrastructure.Caching;

namespace HappyWechat.Infrastructure.MessageProcessing;

/// <summary>
/// EYun回调处理器 - 完整实现消息流水线
/// 集成 WxManagerIdCacheService, EnhancedSensitiveWordService, AdvancedMessageSplitter, UnifiedMediaProcessor
/// </summary>
public class EYunCallbackProcessor : IEYunCallbackProcessor
{
    private readonly ILogger<EYunCallbackProcessor> _logger;
    private readonly ISimplifiedQueueService _queueService;
    private readonly IWxManagerIdCacheService _wxManagerIdService;
    private readonly IUnifiedIdManager _unifiedIdManager;
    private readonly IEnhancedSensitiveWordService _sensitiveWordService;
    private readonly IUnifiedMediaProcessor _mediaProcessor;
    private readonly IAiConfigHotReloadService _aiConfigService;

    // 支持的消息类型 - 来自用户需求的10种类型
    private static readonly HashSet<string> SupportedMessageTypes = new()
    {
        MessageTypeConstants.PRIVATE_TEXT,      // 60001
        MessageTypeConstants.PRIVATE_IMAGE,     // 60002
        MessageTypeConstants.PRIVATE_VOICE,     // 60004
        MessageTypeConstants.PRIVATE_FILE,      // 60009
        MessageTypeConstants.GROUP_TEXT,        // 80001
        MessageTypeConstants.GROUP_IMAGE,       // 80002
        MessageTypeConstants.GROUP_VOICE,       // 80004
        MessageTypeConstants.GROUP_FILE,        // 80009
        MessageTypeConstants.OFFLINE_NOTIFICATION, // 30000
        "30001" // 系统消息（朋友请求等）
    };

    public EYunCallbackProcessor(
        ILogger<EYunCallbackProcessor> logger,
        ISimplifiedQueueService queueService,
        IWxManagerIdCacheService wxManagerIdService,
        IUnifiedIdManager unifiedIdManager,
        IEnhancedSensitiveWordService sensitiveWordService,
        IUnifiedMediaProcessor mediaProcessor,
        IAiConfigHotReloadService aiConfigService)
    {
        _logger = logger;
        _queueService = queueService;
        _wxManagerIdService = wxManagerIdService;
        _unifiedIdManager = unifiedIdManager;
        _sensitiveWordService = sensitiveWordService;
        _mediaProcessor = mediaProcessor;
        _aiConfigService = aiConfigService;
    }

    public async Task<bool> ProcessCallbackAsync(object callbackData)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 📥 收到EYun回调数据", processingId);

            // 1. 解析回调数据为DTO
            var callbackDto = await ParseCallbackDataAsync(callbackData, processingId);
            if (callbackDto == null)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 回调数据解析失败，跳过处理", processingId);
                return false;
            }

            // 2. WcId到WxManagerId映射 - 使用统一ID管理器
            var wxManagerId = await _unifiedIdManager.ResolveWxManagerIdAsync(callbackDto.WcId);
            if (!wxManagerId.HasValue)
            {
                // 尝试确保映射存在
                var mappingEstablished = await _unifiedIdManager.EnsureMappingAsync(callbackDto.WcId!);
                if (!mappingEstablished)
                {
                    _logger.LogWarning("[{ProcessingId}] ⚠️ 无效的WcId: {WcId}，跳过处理", processingId, callbackDto.WcId);
                    return false;
                }
                
                // 重新获取映射
                wxManagerId = await _unifiedIdManager.ResolveWxManagerIdAsync(callbackDto.WcId);
                if (!wxManagerId.HasValue)
                {
                    _logger.LogError("[{ProcessingId}] ❌ 映射建立失败 - WcId: {WcId}", processingId, callbackDto.WcId);
                    return false;
                }
            }

            callbackDto.WxManagerId = wxManagerId.Value;

            // 3. 消息类型验证
            if (!IsSupportedMessageType(callbackDto.Type))
            {
                _logger.LogDebug("[{ProcessingId}] 🚫 不支持的消息类型: {MessageType}，跳过处理", processingId, callbackDto.Type);
                return true; // 不支持的消息类型不算错误
            }

            // 4. 路由到对应的处理队列
            var success = await RouteToProcessingQueueAsync(callbackDto, processingId);
            
            if (success)
            {
                _logger.LogInformation("[{ProcessingId}] ✅ 回调数据处理完成 - MessageType: {MessageType}, WxManagerId: {WxManagerId}", 
                    processingId, callbackDto.Type, wxManagerId.Value);
            }
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 回调数据处理异常", processingId);
            return false;
        }
    }

    public async Task<bool> ProcessMessageAsync(WxMessage message)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 📨 处理微信消息 - MessageId: {MessageId}, Type: {Type}", 
                processingId, message.Id, message.Type);

            // 转换为回调DTO格式并处理
            var callbackDto = ConvertMessageToDto(message);
            return await ProcessCallbackAsync(callbackDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 微信消息处理失败 - MessageId: {MessageId}", 
                processingId, message.Id);
            return false;
        }
    }

    public async Task<bool> ProcessFriendRequestAsync(object friendRequest)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 👥 处理好友请求", processingId);

            // 创建好友请求DTO
            var friendRequestDto = new WxCallbackDto
            {
                Type = "30001", // 系统消息 - 好友请求
                Content = Newtonsoft.Json.JsonConvert.SerializeObject(friendRequest),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            return await ProcessCallbackAsync(friendRequestDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 好友请求处理失败", processingId);
            return false;
        }
    }

    public async Task<bool> ProcessGroupInviteAsync(object groupInvite)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        
        try
        {
            _logger.LogInformation("[{ProcessingId}] 👥 处理群邀请", processingId);

            // 创建群邀请DTO
            var groupInviteDto = new WxCallbackDto
            {
                Type = "30001", // 系统消息 - 群邀请
                Content = Newtonsoft.Json.JsonConvert.SerializeObject(groupInvite),
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            return await ProcessCallbackAsync(groupInviteDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 群邀请处理失败", processingId);
            return false;
        }
    }

    /// <summary>
    /// 解析回调数据为DTO
    /// </summary>
    private async Task<WxCallbackDto?> ParseCallbackDataAsync(object callbackData, string processingId)
    {
        try
        {
            string jsonString;
            
            if (callbackData is string str)
            {
                jsonString = str;
            }
            else
            {
                jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(callbackData);
            }

            // 先解析为EYun原生格式
            var eyunDto = Newtonsoft.Json.JsonConvert.DeserializeObject<WxCallbackMessageDto>(jsonString);
            if (eyunDto == null)
            {
                _logger.LogError("[{ProcessingId}] ❌ EYun数据解析失败", processingId);
                return null;
            }

            // 转换为内部DTO
            var dto = new WxCallbackDto 
            {
                WcId = eyunDto.WcId,
                Type = eyunDto.MessageType,
                FromUser = eyunDto.Data?.FromUser,
                ToUser = eyunDto.Data?.ToUser,
                Content = eyunDto.Data?.Content,
                Timestamp = eyunDto.Data?.Timestamp ?? 0,
                WxManagerId = null, // 后续通过查找设置
                
                // 处理群组信息
                GroupName = eyunDto.Data?.FromGroup
            };

            // 处理特殊消息类型的字段
            if (eyunDto.Data != null)
            {
                // 对于图片、语音、文件等类型，从content中提取URL信息
                if (eyunDto.MessageType == MessageTypeConstants.PRIVATE_IMAGE || 
                    eyunDto.MessageType == MessageTypeConstants.GROUP_IMAGE)
                {
                    dto.ImageUrl = ExtractUrlFromXml(eyunDto.Data.Content);
                }
                else if (eyunDto.MessageType == MessageTypeConstants.PRIVATE_VOICE || 
                         eyunDto.MessageType == MessageTypeConstants.GROUP_VOICE)
                {
                    dto.VoiceUrl = ExtractUrlFromXml(eyunDto.Data.Content);
                }
                else if (eyunDto.MessageType == MessageTypeConstants.PRIVATE_FILE || 
                         eyunDto.MessageType == MessageTypeConstants.GROUP_FILE)
                {
                    dto.FileName = ExtractFileNameFromXml(eyunDto.Data.Content);
                }
            }

            _logger.LogDebug("[{ProcessingId}] 📋 回调数据解析成功 - Type: {Type}, WcId: {WcId}, FromUser: {FromUser}", 
                processingId, dto.Type, dto.WcId, dto.FromUser);

            return dto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 回调数据解析失败 - Data: {Data}", 
                processingId, callbackData?.ToString()?[..Math.Min(200, callbackData?.ToString()?.Length ?? 0)]);
            return null;
        }
    }

    /// <summary>
    /// 从XML内容中提取URL
    /// </summary>
    private string? ExtractUrlFromXml(string? content)
    {
        if (string.IsNullOrEmpty(content)) return null;
        
        try
        {
            // 简单的XML解析，提取URL相关信息
            // 根据实际EYun数据格式调整
            var startIndex = content.IndexOf("url=\"");
            if (startIndex >= 0)
            {
                startIndex += 5; // "url=\"".Length
                var endIndex = content.IndexOf("\"", startIndex);
                if (endIndex > startIndex)
                {
                    return content.Substring(startIndex, endIndex - startIndex);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "从XML内容中提取URL失败: {Content}", content);
        }
        
        return null;
    }

    /// <summary>
    /// 从XML内容中提取文件名
    /// </summary>
    private string? ExtractFileNameFromXml(string? content)
    {
        if (string.IsNullOrEmpty(content)) return null;
        
        try
        {
            // 简单的XML解析，提取文件名相关信息
            var startIndex = content.IndexOf("filename=\"");
            if (startIndex >= 0)
            {
                startIndex += 10; // "filename=\"".Length
                var endIndex = content.IndexOf("\"", startIndex);
                if (endIndex > startIndex)
                {
                    return content.Substring(startIndex, endIndex - startIndex);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "从XML内容中提取文件名失败: {Content}", content);
        }
        
        return null;
    }

    /// <summary>
    /// 获取WxManagerId
    /// </summary>
    private async Task<Guid> GetWxManagerIdAsync(string? wcId, string processingId)
    {
        if (string.IsNullOrEmpty(wcId))
        {
            _logger.LogWarning("[{ProcessingId}] ⚠️ WcId为空", processingId);
            return Guid.Empty;
        }

        try
        {
            var wxManagerId = await _wxManagerIdService.GetWxManagerIdAsync(wcId);
            
            if (wxManagerId == null || wxManagerId == Guid.Empty)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 无法获取WxManagerId - WcId: {WcId}", processingId, wcId);
                return Guid.Empty;
            }
            else
            {
                _logger.LogDebug("[{ProcessingId}] 🔗 WcId映射成功 - WcId: {WcId} -> WxManagerId: {WxManagerId}", 
                    processingId, wcId, wxManagerId);
            }

            return wxManagerId.Value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ WxManagerId获取异常 - WcId: {WcId}", processingId, wcId);
            return Guid.Empty;
        }
    }

    /// <summary>
    /// 检查是否为支持的消息类型
    /// </summary>
    private bool IsSupportedMessageType(string? messageType)
    {
        return !string.IsNullOrEmpty(messageType) && SupportedMessageTypes.Contains(messageType);
    }

    /// <summary>
    /// 路由到相应的处理队列
    /// </summary>
    private async Task<bool> RouteToProcessingQueueAsync(WxCallbackDto callbackDto, string processingId)
    {
        try
        {
            var queueType = DetermineQueueType(callbackDto.Type!);
            
            _logger.LogDebug("[{ProcessingId}] 📤 路由消息到队列 - QueueType: {QueueType}, MessageType: {MessageType}", 
                processingId, queueType, callbackDto.Type);

            // 对于媒体消息，预处理媒体内容
            if (IsMediaMessage(callbackDto.Type!) && !string.IsNullOrEmpty(callbackDto.Content))
            {
                await PreprocessMediaMessageAsync(callbackDto, processingId);
            }

            // 对于文本消息，进行敏感词检测
            if (IsTextMessage(callbackDto.Type!) && !string.IsNullOrEmpty(callbackDto.Content))
            {
                await PreprocessTextMessageAsync(callbackDto, processingId);
            }

            // 验证WxManagerId
            if (!callbackDto.WxManagerId.HasValue)
            {
                _logger.LogError("[{ProcessingId}] ❌ WxManagerId为空，无法路由消息", processingId);
                return false;
            }

            // 入队到相应的处理队列
            var messageId = await _queueService.EnqueueAsync(
                callbackDto.WxManagerId.Value,
                queueType,
                callbackDto,
                priority: 0,
                maxRetryCount: 3,
                properties: new Dictionary<string, object>
                {
                    { "processing_id", processingId },
                    { "message_type", callbackDto.Type! },
                    { "received_at", DateTime.UtcNow.ToString("O") }
                });

            _logger.LogInformation("[{ProcessingId}] ✅ 消息已入队 - QueueType: {QueueType}, MessageId: {MessageId}", 
                processingId, queueType, messageId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 消息路由失败 - MessageType: {MessageType}", 
                processingId, callbackDto.Type);
            return false;
        }
    }

    /// <summary>
    /// 确定队列类型
    /// </summary>
    private string DetermineQueueType(string messageType)
    {
        return messageType switch
        {
            // 文本消息和媒体消息都发送到AI处理队列
            MessageTypeConstants.PRIVATE_TEXT or MessageTypeConstants.GROUP_TEXT => "AiMessage",
            MessageTypeConstants.PRIVATE_IMAGE or MessageTypeConstants.GROUP_IMAGE => "AiMessage",
            MessageTypeConstants.PRIVATE_VOICE or MessageTypeConstants.GROUP_VOICE => "AiMessage",
            MessageTypeConstants.PRIVATE_FILE or MessageTypeConstants.GROUP_FILE => "AiMessage",
            
            // 系统消息发送到专门的系统队列
            MessageTypeConstants.OFFLINE_NOTIFICATION => "SystemNotification",
            "30001" => "FriendRequest",
            
            // 默认发送到通用消息队列
            _ => "WxCallback"
        };
    }

    /// <summary>
    /// 预处理媒体消息
    /// </summary>
    private async Task PreprocessMediaMessageAsync(WxCallbackDto callbackDto, string processingId)
    {
        try
        {
            // 检查是否为支持的媒体类型
            if (!_mediaProcessor.IsMediaMessageType(callbackDto.Type!))
            {
                _logger.LogDebug("[{ProcessingId}] 📁 非媒体消息，跳过媒体预处理 - Type: {Type}", 
                    processingId, callbackDto.Type);
                return;
            }

            _logger.LogDebug("[{ProcessingId}] 🎬 开始媒体消息预处理 - Type: {Type}", 
                processingId, callbackDto.Type);

            // 标记为需要媒体处理
            callbackDto.Content = Newtonsoft.Json.JsonConvert.SerializeObject(new
            {
                original_content = callbackDto.Content,
                requires_media_processing = true,
                processing_id = processingId,
                media_type = callbackDto.Type
            });

            _logger.LogDebug("[{ProcessingId}] ✅ 媒体消息预处理完成", processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 媒体消息预处理失败", processingId);
        }
    }

    /// <summary>
    /// 预处理文本消息
    /// </summary>
    private async Task PreprocessTextMessageAsync(WxCallbackDto callbackDto, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 📝 开始文本消息预处理", processingId);

            // 敏感词检测
            var sensitiveResult = await _sensitiveWordService.CheckIncomingMessageAsync(
                callbackDto.Content!,
                callbackDto.WxManagerId.ToString());

            if (sensitiveResult.ContainsSensitiveWords)
            {
                _logger.LogWarning("[{ProcessingId}] ⚠️ 检测到敏感词 - Count: {Count}, Action: {Action}", 
                    processingId, sensitiveResult.DetectedWords.Count, sensitiveResult.Action);

                // 根据策略处理敏感词
                switch (sensitiveResult.Action)
                {
                    case SensitiveWordActionType.Block:
                        // 阻止消息处理
                        _logger.LogWarning("[{ProcessingId}] 🚫 消息被阻止 - 包含敏感词", processingId);
                        return;
                        
                    case SensitiveWordActionType.Replace:
                        // 替换敏感词
                        callbackDto.Content = sensitiveResult.ProcessedContent;
                        _logger.LogDebug("[{ProcessingId}] 🔄 敏感词已替换", processingId);
                        break;
                        
                    case SensitiveWordActionType.LogOnly:
                        // 仅记录日志，不做处理
                        _logger.LogInformation("[{ProcessingId}] 📝 敏感词已记录 - Words: {Words}", 
                            processingId, string.Join(", ", sensitiveResult.DetectedWords));
                        break;
                }
            }

            _logger.LogDebug("[{ProcessingId}] ✅ 文本消息预处理完成", processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 文本消息预处理失败", processingId);
        }
    }

    /// <summary>
    /// 检查是否为媒体消息
    /// </summary>
    private bool IsMediaMessage(string messageType)
    {
        return messageType is 
            MessageTypeConstants.PRIVATE_IMAGE or MessageTypeConstants.GROUP_IMAGE or
            MessageTypeConstants.PRIVATE_VOICE or MessageTypeConstants.GROUP_VOICE or
            MessageTypeConstants.PRIVATE_FILE or MessageTypeConstants.GROUP_FILE;
    }

    /// <summary>
    /// 检查是否为文本消息
    /// </summary>
    private bool IsTextMessage(string messageType)
    {
        return messageType is MessageTypeConstants.PRIVATE_TEXT or MessageTypeConstants.GROUP_TEXT;
    }

    /// <summary>
    /// 将WxMessage转换为WxCallbackDto
    /// </summary>
    private WxCallbackDto ConvertMessageToDto(WxMessage message)
    {
        return new WxCallbackDto
        {
            // 注意：WxMessage实体缺少WxManagerId和WcId字段，需要从其他方式获取
            WxManagerId = Guid.Empty, // 需要根据实际业务逻辑获取
            WcId = "", // 需要根据实际业务逻辑获取
            Type = message.Type.ToString(),
            FromUser = message.FromUser,
            ToUser = message.ToUser,
            Content = message.Content,
            Timestamp = ((DateTimeOffset)message.CreatedAt).ToUnixTimeSeconds()
        };
    }
}