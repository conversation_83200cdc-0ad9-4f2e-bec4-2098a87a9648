using Microsoft.Extensions.Logging;
using HappyWechat.Application.DTOs.AI;
using HappyWechat.Application.Interfaces;

namespace HappyWechat.Infrastructure.Services;

/// <summary>
/// 简化AI服务实现
/// </summary>
public class SimplifiedAiService : IAiService
{
    private readonly ILogger<SimplifiedAiService> _logger;

    public SimplifiedAiService(ILogger<SimplifiedAiService> logger)
    {
        _logger = logger;
    }

    public async Task<AiMessageDto?> ProcessMessageAsync(AiMessageDto message, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🤖 处理AI消息 - Content: {Content}", 
                message.Content?.Substring(0, Math.Min(50, message.Content?.Length ?? 0)));

            // 模拟AI处理
            await Task.Delay(100, cancellationToken);

            var response = new AiMessageDto
            {
                WxManagerId = message.WxManagerId,
                Content = $"AI回复: {message.Content}",
                MessageType = "text",
                FromUser = "AI",
                ToUser = message.FromUser,
                IsGroupMessage = message.IsGroupMessage,
                GroupId = message.GroupId,
                CreatedAt = DateTime.UtcNow
            };

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ AI消息处理失败");
            return null;
        }
    }

    public async Task<string?> GenerateReplyAsync(string content, string context, CancellationToken cancellationToken = default)
    {
        try
        {
            // 模拟AI回复生成
            await Task.Delay(50, cancellationToken);
            return $"AI回复: {content}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ AI回复生成失败");
            return null;
        }
    }
}