[ENTRYPOINT] HappyWechat 容器启动脚本开始执行
[ENTRYPOINT] 当前用户: root (UID: 0)
[ENTRYPOINT] 以root用户运行，将修复权限后切换到应用用户
[ENTRYPOINT] 执行权限修复流程...
[ENTRYPOINT] 以root身份修复权限...
[ENTRYPOINT] 创建必要目录...
[ENTRYPOINT] 创建目录: /app/.aspnet/DataProtection-Keys/escrow
[ENTRYPOINT] 创建目录: /app/logs
[SUCCESS] 目录创建完成
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys
[ENTRYPOINT] 数据保护备份目录已创建
[ENTRYPOINT] 数据保护目录设置为700权限: /app/.aspnet/DataProtection-Keys/escrow
[SUCCESS] 权限修复完成
[ENTRYPOINT] 切换到应用用户并启动应用
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建数据库连接字符串: Server=172.19.0.2;Port=3306;Database=huakai
info: HappyWechat.Infrastructure.Configuration.EnvironmentVariableConfigurationProvider[0]
      🐳 Docker环境-构建Redis连接字符串: 172.19.0.3:6379
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.ServiceRegistration.ServiceHealthValidator[0]
      ✅ 启动验证成功完成，耗时: 3.9627ms
warn: Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager[35]
      No XML encryptor configured. Key {b12abad8-8629-4468-80f2-06b909012208} may be persisted to storage in unencrypted form.
warn: Microsoft.AspNetCore.Hosting.Diagnostics[15]
      Overriding HTTP_PORTS '8080' and HTTPS_PORTS ''. Binding to values defined by URLS instead 'http://+:5215'.
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 1411ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 1734ms
warn: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射健康检查异常 - 缓存命中率: 0.00 %, 响应时间: 0.00ms, 覆盖率: 100.00 %
warn: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      健康检查问题: 缓存命中率偏低: 0.00 %
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      检测到缓存命中率过低，触发预热操作
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      开始执行ID映射缓存预热...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      开始预热活跃账号缓存...
info: HappyWechat.Infrastructure.IdManagement.UnifiedIdManager[0]
      缓存预热完成 - 账号数: 1, 耗时: 6ms
info: HappyWechat.Infrastructure.IdManagement.IdMappingHealthService[0]
      ID映射缓存预热完成 - 账号数: 1, 耗时: 14ms
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [fb7af256] 📥 收到EYun回调数据
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [fb7af256] ✅ 消息已入队 - QueueType: AiMessage, MessageId: 91e8ce47-c784-49b1-96e5-786e79f50f49
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [fb7af256] ✅ 回调数据处理完成 - MessageType: 60001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [170dac5c] ✅ 回调处理成功
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [3e9e2585] 📥 收到EYun回调数据
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [3e9e2585] ✅ 消息已入队 - QueueType: AiMessage, MessageId: d989e073-e5fe-411e-80e4-5903961527c8
info: HappyWechat.Infrastructure.MessageProcessing.EYunCallbackProcessor[0]
      [3e9e2585] ✅ 回调数据处理完成 - MessageType: 80001, WxManagerId: 980a47bc-1452-446c-8e78-72f7bb0c5144
info: HappyWechat.Web.Controllers.WxController[0]
      [a5f3d9b2] ✅ 回调处理成功
