using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Application.Interfaces;
using HappyWechat.Infrastructure.Identity.Repositories;
using HappyWechat.Infrastructure.MessageQueue.Core;
using HappyWechat.Infrastructure.MessageQueue.Models;
using HappyWechat.Infrastructure.MessageProcessing.Configuration;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 好友请求处理器接口
/// </summary>
public interface IFriendRequestProcessor
{
    /// <summary>
    /// 处理好友请求
    /// </summary>
    Task<FriendRequestProcessResult> ProcessFriendRequestAsync(WxCallbackMessageDto callbackMessage, GlobalRobotConfig globalConfig, string processingId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取今日好友通过统计
    /// </summary>
    Task<FriendAcceptStatistics> GetTodayFriendAcceptStatisticsAsync(Guid wxManagerId);
}

/// <summary>
/// 好友请求处理结果
/// </summary>
public class FriendRequestProcessResult
{
    public bool Success { get; set; }
    public bool Accepted { get; set; }
    public string? Reason { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> ExtendedData { get; set; } = new();

    public static FriendRequestProcessResult CreateAccepted(string reason)
    {
        return new FriendRequestProcessResult
        {
            Success = true,
            Accepted = true,
            Reason = reason
        };
    }

    public static FriendRequestProcessResult CreateRejected(string reason)
    {
        return new FriendRequestProcessResult
        {
            Success = true,
            Accepted = false,
            Reason = reason
        };
    }

    public static FriendRequestProcessResult CreateError(string errorMessage)
    {
        return new FriendRequestProcessResult
        {
            Success = false,
            Accepted = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 好友通过统计
/// </summary>
public class FriendAcceptStatistics
{
    public Guid WxManagerId { get; set; }
    public DateTime Date { get; set; }
    public int AcceptedCount { get; set; }
    public int RejectedCount { get; set; }
    public int TotalRequests { get; set; }
    public List<FriendRequestRecord> RecentRequests { get; set; } = new();
}

/// <summary>
/// 好友请求记录
/// </summary>
public class FriendRequestRecord
{
    public string FromUser { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public DateTime RequestTime { get; set; }
    public bool Accepted { get; set; }
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 好友请求处理器实现
/// </summary>
public class FriendRequestProcessor : IFriendRequestProcessor
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IUnifiedMessageRouter _messageRouter;
    private readonly ISystemConfigManager _configManager;
    private readonly ILogger<FriendRequestProcessor> _logger;
    private readonly IServiceProvider _serviceProvider;

    public FriendRequestProcessor(
        ApplicationDbContext dbContext,
        IUnifiedMessageRouter messageRouter,
        ISystemConfigManager configManager,
        ILogger<FriendRequestProcessor> logger,
        IServiceProvider serviceProvider)
    {
        _dbContext = dbContext;
        _messageRouter = messageRouter;
        _configManager = configManager;
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task<FriendRequestProcessResult> ProcessFriendRequestAsync(WxCallbackMessageDto callbackMessage, GlobalRobotConfig globalConfig, string processingId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("[{ProcessingId}] 👥 开始处理好友请求 - FromUser: {FromUser}", 
                processingId, callbackMessage.Data?.FromUser);

            // 检查是否启用自动通过好友
            if (!globalConfig.EnableAutoAcceptFriend)
            {
                _logger.LogDebug("[{ProcessingId}] ⏭️ 自动通过好友功能未启用", processingId);
                return FriendRequestProcessResult.CreateRejected("自动通过好友功能未启用");
            }

            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            var fromUser = callbackMessage.Data?.FromUser ?? "";
            var content = callbackMessage.Data?.Content ?? "";

            // 检查关键词匹配
            var keywordMatchResult = CheckKeywordMatch(content, globalConfig.FriendAcceptKeywords, processingId);
            if (!keywordMatchResult.matched)
            {
                await RecordFriendRequestAsync(wxManagerId, fromUser, content, false, keywordMatchResult.reason);
                return FriendRequestProcessResult.CreateRejected(keywordMatchResult.reason);
            }

            // 检查每日限制
            var todayStats = await GetTodayFriendAcceptStatisticsAsync(wxManagerId);
            if (todayStats.AcceptedCount >= globalConfig.DailyFriendAcceptLimit)
            {
                var reason = $"今日通过好友数量已达限制 ({todayStats.AcceptedCount}/{globalConfig.DailyFriendAcceptLimit})";
                _logger.LogWarning("[{ProcessingId}] ⚠️ {Reason}", processingId, reason);
                
                await RecordFriendRequestAsync(wxManagerId, fromUser, content, false, reason);
                return FriendRequestProcessResult.CreateRejected(reason);
            }

            // 检查是否已经是好友
            var isAlreadyFriend = await CheckIfAlreadyFriendAsync(wxManagerId, fromUser);
            if (isAlreadyFriend)
            {
                var reason = "用户已经是好友";
                _logger.LogDebug("[{ProcessingId}] ℹ️ {Reason} - FromUser: {FromUser}", processingId, reason, fromUser);
                
                await RecordFriendRequestAsync(wxManagerId, fromUser, content, false, reason);
                return FriendRequestProcessResult.CreateRejected(reason);
            }

            // 执行自动通过好友
            var acceptResult = await AcceptFriendRequestAsync(callbackMessage, processingId, cancellationToken);
            if (!acceptResult.success)
            {
                await RecordFriendRequestAsync(wxManagerId, fromUser, content, false, acceptResult.errorMessage);
                return FriendRequestProcessResult.CreateError(acceptResult.errorMessage);
            }

            // 记录成功通过的好友请求
            await RecordFriendRequestAsync(wxManagerId, fromUser, content, true, "自动通过");

            // 发送欢迎语
            if (!string.IsNullOrEmpty(globalConfig.FriendWelcomeMessage))
            {
                await SendWelcomeMessageAsync(callbackMessage, globalConfig.FriendWelcomeMessage, processingId, cancellationToken);
            }

            _logger.LogInformation("[{ProcessingId}] ✅ 好友请求处理完成 - FromUser: {FromUser}, Accepted: true", 
                processingId, fromUser);

            return FriendRequestProcessResult.CreateAccepted("自动通过好友请求");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 处理好友请求异常", processingId);
            return FriendRequestProcessResult.CreateError($"处理好友请求异常: {ex.Message}");
        }
    }

    public async Task<FriendAcceptStatistics> GetTodayFriendAcceptStatisticsAsync(Guid wxManagerId)
    {
        try
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);

            // 这里需要根据实际的好友请求记录表来查询
            // 暂时返回模拟数据
            return new FriendAcceptStatistics
            {
                WxManagerId = wxManagerId,
                Date = today,
                AcceptedCount = 0, // 实际应该从数据库查询
                RejectedCount = 0,
                TotalRequests = 0,
                RecentRequests = new List<FriendRequestRecord>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取今日好友通过统计失败 - WxManagerId: {WxManagerId}", wxManagerId);
            return new FriendAcceptStatistics
            {
                WxManagerId = wxManagerId,
                Date = DateTime.Today
            };
        }
    }

    /// <summary>
    /// 检查关键词匹配
    /// </summary>
    private (bool matched, string reason) CheckKeywordMatch(string content, List<string> keywords, string processingId)
    {
        if (!keywords.Any())
        {
            _logger.LogDebug("[{ProcessingId}] ⚠️ 未配置好友通过关键词", processingId);
            return (false, "未配置好友通过关键词");
        }

        foreach (var keyword in keywords)
        {
            if (content.Contains(keyword, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogDebug("[{ProcessingId}] ✅ 匹配到关键词: {Keyword}", processingId, keyword);
                return (true, $"匹配关键词: {keyword}");
            }
        }

        _logger.LogDebug("[{ProcessingId}] ❌ 未匹配到任何关键词 - Content: {Content}", processingId, content);
        return (false, "未匹配到通过关键词");
    }

    /// <summary>
    /// 检查是否已经是好友
    /// </summary>
    private async Task<bool> CheckIfAlreadyFriendAsync(Guid wxManagerId, string fromUser)
    {
        try
        {
            return await _dbContext.WxContactEntities
                .AnyAsync(c => c.WxManagerId == wxManagerId && c.WcId == fromUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查是否已经是好友失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}", 
                wxManagerId, fromUser);
            return false;
        }
    }

    /// <summary>
    /// 执行自动通过好友请求
    /// </summary>
    private async Task<(bool success, string errorMessage)> AcceptFriendRequestAsync(WxCallbackMessageDto callbackMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🤝 开始执行自动通过好友请求", processingId);

            // 调用EYun API通过好友请求
            // 这里需要根据实际的API接口来实现
            // 暂时返回成功
            
            _logger.LogDebug("[{ProcessingId}] ✅ 好友请求通过成功", processingId);
            return (true, "");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 执行自动通过好友请求失败", processingId);
            return (false, ex.Message);
        }
    }

    /// <summary>
    /// 发送欢迎语
    /// </summary>
    private async Task SendWelcomeMessageAsync(WxCallbackMessageDto callbackMessage, string welcomeMessage, string processingId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 💬 开始发送好友欢迎语", processingId);

            // 🔧 获取微信账号信息
            var managerGuid = Guid.Parse(callbackMessage.WxManagerId);
            var wxManagerInfo = await GetWxManagerInfoAsync(managerGuid);
            if (wxManagerInfo == null)
            {
                _logger.LogError("[{ProcessingId}] ❌ 无法获取微信账号信息，跳过发送欢迎语", processingId);
                return;
            }

            var welcomeRequest = new SendMessageRequest
            {
                MessageId = Guid.NewGuid().ToString(),
                WxManagerId = managerGuid,
                WId = wxManagerInfo.Value.WId, // 🔧 设置EYun登录实例标识
                WcId = callbackMessage.Data?.FromUser ?? "", // 🔧 设置接收人微信ID
                MessageType = SendMessageType.Text,
                ToUser = callbackMessage.Data?.FromUser ?? "", // 保持兼容性
                Content = welcomeMessage,
                Priority = HappyWechat.Infrastructure.MessageQueue.Models.MessagePriority.Normal,
                CreatedTime = DateTime.UtcNow,
                Metadata = new Dictionary<string, object>
                {
                    ["MessageSource"] = "FriendWelcome",
                    ["ProcessingId"] = processingId
                }
            };

            var wxManagerId = Guid.Parse(callbackMessage.WxManagerId);
            await _messageRouter.RouteMessageSendAsync(welcomeRequest, wxManagerId, 3000, cancellationToken); // 延迟3秒发送

            _logger.LogDebug("[{ProcessingId}] ✅ 好友欢迎语已发送", processingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 发送好友欢迎语失败", processingId);
        }
    }

    /// <summary>
    /// 记录好友请求
    /// </summary>
    private async Task RecordFriendRequestAsync(Guid wxManagerId, string fromUser, string content, bool accepted, string reason)
    {
        try
        {
            // 这里需要根据实际的数据库设计来记录好友请求
            // 暂时只记录日志
            _logger.LogInformation("好友请求记录 - WxManagerId: {WxManagerId}, FromUser: {FromUser}, Accepted: {Accepted}, Reason: {Reason}",
                wxManagerId, fromUser, accepted, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录好友请求失败 - WxManagerId: {WxManagerId}, FromUser: {FromUser}",
                wxManagerId, fromUser);
        }
    }

    /// <summary>
    /// 获取微信管理器信息
    /// </summary>
    private async Task<(string WId, string WcId)?> GetWxManagerInfoAsync(Guid managerGuid)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            var wxManager = await dbContext.WxMangerEntities
                .Where(w => w.Id == managerGuid && w.IsEnabled)
                .Select(w => new { w.WId, w.WcId, w.WxStatus })
                .FirstOrDefaultAsync();

            if (wxManager == null)
            {
                _logger.LogError("❌ 未找到WxManagerId对应的微信账号 - WxManagerId: {WxManagerId}", managerGuid);
                return null;
            }

            if (string.IsNullOrEmpty(wxManager.WId))
            {
                _logger.LogError("❌ 微信账号的WId为空 - WxManagerId: {WxManagerId}", managerGuid);
                return null;
            }

            if (string.IsNullOrEmpty(wxManager.WcId))
            {
                _logger.LogError("❌ 微信账号的WcId为空 - WxManagerId: {WxManagerId}", managerGuid);
                return null;
            }

            _logger.LogDebug("✅ 获取微信账号信息成功 - WxManagerId: {WxManagerId}, WId: {WId}, WcId: {WcId}, Status: {Status}",
                managerGuid, wxManager.WId, wxManager.WcId, wxManager.WxStatus);

            return (wxManager.WId, wxManager.WcId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 获取微信账号信息失败 - WxManagerId: {WxManagerId}", managerGuid);
            return null;
        }
    }
}
