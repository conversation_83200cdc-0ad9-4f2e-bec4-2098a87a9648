using HappyWechat.Application.DTOs.Wrappers.EYun;
using HappyWechat.Infrastructure.MessageProcessing.Models;
using HappyWechat.Infrastructure.MessageProcessing.Strategies;
using HappyWechat.Infrastructure.MessageProcessing.Constants;
using Microsoft.Extensions.Logging;

namespace HappyWechat.Infrastructure.MessageProcessing.Services;

/// <summary>
/// 统一消息目标解析器 - 新架构实现
/// </summary>
public class UnifiedMessageTargetResolver
{
    private readonly ILogger<UnifiedMessageTargetResolver> _logger;
    private readonly IMessageRoutingStrategy _messageRoutingStrategy;

    public UnifiedMessageTargetResolver(
        ILogger<UnifiedMessageTargetResolver> logger,
        IMessageRoutingStrategy messageRoutingStrategy)
    {
        _logger = logger;
        _messageRoutingStrategy = messageRoutingStrategy;
    }

    /// <summary>
    /// 解析消息目标
    /// </summary>
    public async Task<MessageTarget> ResolveTargetAsync(WxCallbackMessageDto callbackMessage, dynamic aiConfig, string processingId)
    {
        try
        {
            _logger.LogDebug("[{ProcessingId}] 🎯 开始解析消息目标 - MessageType: {MessageType}, IsGroupMessage: {IsGroupMessage}",
                processingId, callbackMessage.MessageType, (bool)aiConfig.IsGroupMessage);

            // 使用新的消息路由策略
            var decision = await _messageRoutingStrategy.RouteMessageAsync(callbackMessage, aiConfig, processingId);
            
            if (!decision.ShouldProcess)
            {
                _logger.LogDebug("[{ProcessingId}] ⏭️ 消息跳过处理 - Reason: {Reason}", processingId, (object)decision.Reason);
                throw new InvalidOperationException($"消息不应处理: {decision.Reason}");
            }

            if (decision.ShouldWaitForCombination)
            {
                _logger.LogDebug("[{ProcessingId}] ⏳ 消息等待文件+@组合 - Reason: {Reason}", processingId, (object)decision.Reason);
                throw new InvalidOperationException($"消息等待组合: {decision.Reason}");
            }

            // 创建消息目标
            var target = new MessageTarget
            {
                TargetUser = decision.TargetUser ?? "",
                AtUsers = decision.AtUsers,
                SendMode = decision.SendMode,
                Content = decision.CombinedContent ?? callbackMessage.Data?.Content ?? ""
            };

            // 🔧 精简日志：注释掉消息目标解析成功日志，减少日志噪音
            // _logger.LogInformation("[{ProcessingId}] ✅ 消息目标解析成功 - TargetUser: {TargetUser}, SendMode: {SendMode}",
            //     processingId, target.TargetUser, target.SendMode);

            return target;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{ProcessingId}] ❌ 解析消息目标异常", processingId);
            throw;
        }
    }
}

/// <summary>
/// 消息目标
/// </summary>
public class MessageTarget
{
    /// <summary>
    /// 目标用户
    /// </summary>
    public string TargetUser { get; set; } = string.Empty;
    
    /// <summary>
    /// @用户列表
    /// </summary>
    public string? AtUsers { get; set; }
    
    /// <summary>
    /// 发送模式
    /// </summary>
    public MessageSendMode SendMode { get; set; }
    
    /// <summary>
    /// 消息内容
    /// </summary>
    public string Content { get; set; } = string.Empty;
}
